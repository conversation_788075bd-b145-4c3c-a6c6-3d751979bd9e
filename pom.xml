<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.dtstack.dtcenter</groupId>
    <artifactId>common-loader</artifactId>
    <version>1.8.4-RELEASE</version>
    <modules>
        <module>core</module>
        <module>rdbms</module>
        <module>clickhouse</module>
        <module>db2</module>
        <module>dm</module>
        <module>es5</module>
        <module>es</module>
        <module>es7</module>
        <module>gbase</module>
        <module>greenplum6</module>
        <module>hbase</module>
        <module>hdfs</module>
        <module>hive</module>
        <module>hive1</module>
        <module>hive3</module>
        <module>impala</module>
        <module>kudu</module>
<!--        <module>kylin</module>-->
<!--        <module>libra</module>-->
        <module>maxcompute</module>
        <module>mysql5</module>
        <module>mysql8</module>
        <module>mariaDB</module>
        <module>oracle</module>
<!--        <module>phoenix</module>-->
        <module>postgresql</module>
        <module>sqlserver</module>
        <module>sqlserver2017</module>
        <module>sybase</module>
        <module>sybase_jtds</module>
        <module>mongo</module>
        <module>redis</module>
        <module>ftp</module>
        <module>kafka</module>
<!--        <module>emq</module>-->
<!--        <module>phoenix5</module>-->
        <module>hbase2</module>
<!--        <module>spark</module>-->
        <module>kingbase8</module>
        <module>common</module>
        <module>kerberos</module>
        <module>huawei_hive3</module>
<!--        <module>hbase_gateway</module>-->
        <!--目前s3驱动依赖版本不好找  暂时注释该模块-->
<!--        <module>s3</module>-->
<!--        <module>websocket</module>-->
<!--        <module>vertica</module>-->
<!--        <module>socket</module>-->
<!--        <module>presto</module>-->
        <module>oceanbase</module>
        <module>oceanbase_for_oracle</module>
        <module>aws_s3</module>
<!--        <module>solr</module>-->
        <module>inceptor</module>
        <module>inceptor8</module>
<!--        <module>influxdb</module>-->
<!--        <module>opentsdb</module>-->
        <module>doris</module>
<!--        <module>phoenix4_8</module>-->
<!--        <module>kylinrestful</module>-->
<!--        <module>restful</module>-->
<!--        <module>trino</module>-->
<!--        <module>dorisrestful</module>-->
<!--        <module>tbds_hbase</module>-->
<!--        <module>tbds_kafka</module>-->
<!--        <module>tbds_hdfs</module>-->
<!--        <module>hive3_cdp</module>-->
        <module>sap_hana</module>
        <module>huawei_hbase</module>
<!--        <module>huawei_kafka</module>-->
        <module>high_version_kafka</module>
        <module>starRocks</module>
        <module>tidb</module>
        <module>tdsql_for_mysql</module>
        <module>goldendb</module>
        <module>informix</module>
        <module>gaussdb</module>
        <module>db2_as400</module>
        <module>sequoiadb_for_mysql</module>
        <module>argodb</module>
        <module>gauss_db200</module>
        <module>oss_ali</module>
        <module>dws_pg</module>
        <module>oss_huawei</module>
        <module>oss_lc</module>
        <module>kundb</module>
        <module>datahub</module>
        <module>oracle_9i</module>
        <module>oracle_19c</module>
        <module>tbase</module>
        <module>tdsql_for_pg</module>
        <module>tdsql_for_oracle</module>
        <module>huawei_hdfs</module>
        <module>huawei_clickhouse</module>
        <module>mysql8_hive</module>
        <module>mysql5_hive</module>
        <module>gaussdb_hive</module>
        <module>inspur_s3</module>
        <module>es8</module>
        <module>adb_postgresql</module>
        <module>greenplum_postgresql</module>
        <module>gaussdb_for_mysql</module>
        <module>analyticdb_mysql</module>
        <module>dws_mysql</module>

        <module>test</module>
    </modules>
    <packaging>pom</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <dtstack.common>4.0.3-SNAPSHOT</dtstack.common>
        <spring.version>5.2.9.RELEASE</spring.version>
        <aggregate.report.dir>test/target/site/jacoco/jacoco.xml</aggregate.report.dir>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Junit 测试相关 -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.12</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-simple</artifactId>
                <version>1.7.26</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>1.2.3</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.21</version>
                <scope>provided</scope>
            </dependency>

            <!-- Junit 测试相关结束 -->

            <!-- 不参与编译相关 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.8</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
<!--                <version>1.2.76</version>-->
                <version>2.0.25</version>
            </dependency>

            <!--       <dependency>
                       <groupId>com.fasterxml.jackson.core</groupId>
                       <artifactId>jackson-databind</artifactId>
                       <version>2.13.2.1</version>
                   </dependency>-->

            <dependency>
                <groupId>com.dtstack.dtcenter</groupId>
                <artifactId>common.loader.core</artifactId>
                <version>${project.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- Spring 相关-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- 不参与编译相关结束 -->

            <!-- core 包依赖相关 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.4</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-math3</artifactId>
                <version>3.6.1</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.11.1</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>27.1-jre</version>
            </dependency>

            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.6</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>17.0.0</version>
            </dependency>
            <!-- core 包依赖相关结束 -->

            <dependency>
                <groupId>com.dtstack.dtcenter</groupId>
                <artifactId>common.loader.rdbms</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dtstack.dtcenter</groupId>
                <artifactId>common.loader.common</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dtstack.dtcenter</groupId>
                <artifactId>common.loader.kerberos</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.dtstack.dtcenter</groupId>
                <artifactId>common.loader.mysql5</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>ant</groupId>
                <artifactId>ant</artifactId>
                <version>1.6.5</version>
            </dependency>

            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>2.1.3</version>
                <exclusions>
                    <exclusion>
                        <artifactId>xml-apis</artifactId>
                        <groupId>xml-apis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.kerby</groupId>
                <artifactId>kerb-util</artifactId>
                <version>2.0.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-1.2-api</artifactId>
                <version>2.10.0</version>
            </dependency>

            <dependency>
                <groupId>net.sourceforge.javacsv</groupId>
                <artifactId>javacsv</artifactId>
                <version>2.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka_2.11</artifactId>
                <version>0.11.0.2</version>
            </dependency>

        </dependencies>

    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.6</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <dateFormat>yyyy.MM.dd HH:mm:ss</dateFormat>
                    <verbose>true</verbose>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <gitDescribe>
                        <always>false</always>
                        <dirty>-dirty</dirty>
                        <forceLongFormat>false</forceLongFormat>
                    </gitDescribe>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <compilerVersion>1.8</compilerVersion>
                    <compilerArgument>-XDignore.symbol.file</compilerArgument>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>3.7.0.1746</version>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.6</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>report</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <profile>
            <id>coverage</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>prepare-agent</id>
                                <goals>
                                    <goal>prepare-agent</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
<!--    <repositories>-->
<!--        <repository>-->
<!--            <id>releases</id>-->
<!--            <url>http://nexus.dev.dtstack.cn/nexus/content/repositories/dtstack-release/</url>-->
<!--        </repository>-->
<!--        <repository>-->
<!--            <id>snapshot</id>-->
<!--            <url>http://nexus.dev.dtstack.cn/nexus/content/repositories/dtstack-snapshot/</url>-->
<!--        </repository>-->
<!--    </repositories>-->
<!--    <distributionManagement>-->
<!--        <repository>-->
<!--            <id>releases</id>-->
<!--            <url>http://112.126.29.199:8081/repository/joyadata/</url>-->
<!--        </repository>-->
<!--        <snapshotRepository>-->
<!--            <id>snapshot</id>-->
<!--            <url>http://112.126.29.199:8081/repository/joyadata/maven-snapshots/</url>-->
<!--        </snapshotRepository>-->
<!--    </distributionManagement>-->
    <distributionManagement>
        <!-- 需要在 maven对应的配置文件setting.xml 中配置对应的账号，保持id一致 -->
        <!-- 版本库 -->
        <repository>
            <id>maven-releases</id>
            <name>maven-releases</name>
            <url>http://112.126.29.199:8081/repository/maven-releases/</url>
        </repository>
        <!-- 快照库 -->
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://112.126.29.199:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
