package com.dtstack.dtcenter.common.loader.ftp;

import com.dtstack.dtcenter.common.loader.common.enums.SftpAuthType;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

@Slf4j
public class SSHHandler {
    public static final String KEY_USERNAME = "username";
    public static final String KEY_PASSWORD = "password";
    public static final String KEY_HOST = "host";
    public static final String KEY_PORT = "port";
    public static final String KEY_TIMEOUT = "timeout";
    public static final String KEY_RSA = "rsaPath";
    public static final String KEY_AUTHENTICATION = "auth";

    public static final int DEFAULT_HOST = 22;

    private Session session;
    private ChannelExec channel;

    private SSHHandler(Session session, ChannelExec channel) {
        this.session = session;
        this.channel = channel;
    }

    /**
     * 获取实例
     *
     * @param sshConfig
     * @return
     */
    public static SSHHandler getInstance(Map<String, String> sshConfig) throws JSchException {
        checkConfig(sshConfig);

        String host = MapUtils.getString(sshConfig, KEY_HOST);
        int port = MapUtils.getIntValue(sshConfig, KEY_PORT, DEFAULT_HOST);
        String username = MapUtils.getString(sshConfig, KEY_USERNAME);
        String password = MapUtils.getString(sshConfig, KEY_PASSWORD);
        String rsaPath = MapUtils.getString(sshConfig, KEY_RSA);
        String authType = MapUtils.getString(sshConfig, KEY_AUTHENTICATION);

        com.jcraft.jsch.Logger logger = new SFTPHandler.SettleLogger();
        JSch.setLogger(logger);
        JSch jsch = new JSch();
        if (SftpAuthType.RSA.getType().toString().equals(authType) && StringUtils.isNotBlank(rsaPath)) {
            //需要添加私钥
            jsch.addIdentity(rsaPath.trim(), "");
        }
        Session session = jsch.getSession(username, host, port);
        if (session == null) {
            throw new DtLoaderException("Failed to establish a connection with the ssh server, please check whether the user name and password are correct");
        }

        if (StringUtils.isBlank(authType) || SftpAuthType.PASSWORD.getType().toString().equals(authType)) {
            //默认走密码验证模式
            session.setPassword(password);
        }
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        session.setConfig(config);
        session.setTimeout(MapUtils.getIntValue(sshConfig, KEY_TIMEOUT, 0));
        session.connect();

        ChannelExec channel = (ChannelExec) session.openChannel("exec");
        channel.connect();

        return new SSHHandler(session, channel);
    }

    /**
     * 参数校验
     *
     * @param sftpConfig
     */
    private static void checkConfig(Map<String, String> sftpConfig) {
        if (sftpConfig == null || sftpConfig.isEmpty()) {
            throw new DtLoaderException("The config of ssh is null");
        }

        if (StringUtils.isEmpty(sftpConfig.get(KEY_HOST))) {
            throw new DtLoaderException("The host of ssh is null");
        }
    }

    /**
     * 获取文件列表
     *
     * @param command
     * @throws JSchException
     * @throws IOException
     */
    public List<String> executeCommand(String command) throws JSchException, IOException {
        if (session == null || !session.isConnected()) {
            throw new JSchException("Not connected to the SSH server.");
        }
        List<String> fileList = new ArrayList<>();
        channel.setCommand("ls -lR " + command);
        channel.setInputStream(System.in);
        channel.setErrStream(System.err);
        java.io.InputStream in = channel.getInputStream();

        // 获取输出流并读取命令结果
        channel.connect();
        byte[] buffer = new byte[1024];
        while (!channel.isClosed()) {
            if (in.available() > 0) {
                int i = in.read(buffer, 0, 1024);
                if (i < 0) {
                    break;
                }
                fileList.add(new String(buffer, 0, i));
            }
        }
        return fileList;
    }

    /**
     * 关闭实例
     */
    public void close() {
        if (channel != null) {
            channel.disconnect();
        }

        if (session != null) {
            session.disconnect();
        }
    }

    /**
     * 设置 Logger 为 DEBUG
     */
    static class SettleLogger implements com.jcraft.jsch.Logger {
        @Override
        public boolean isEnabled(int level) {
            return true;
        }

        @Override
        public void log(int level, String msg) {
            if (log.isDebugEnabled()) {
                log.debug(msg);
            }
        }
    }

}
