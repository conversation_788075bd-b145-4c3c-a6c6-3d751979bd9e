/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.oss_ali;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.OssSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;

import java.util.Objects;

/**
 * oss 工具类
 *
 * <AUTHOR>
 * date：Created in 上午10:07 2021/5/6
 * company: www.dtstack.com
 */
public class Oss_aliUtil {

    private static final Integer TIMEOUT = 60 * 1000;

    /**
     * 获取 aws s3 客户端
     *
     * @param sourceDTO 数据源信息
     * @return aws s3客户端
     */
    public static OSS getClient(OssSourceDTO sourceDTO) {
        String endPoint = sourceDTO.getEndPoint();
        String accessKey = sourceDTO.getAccessKey();
        String secretKey = sourceDTO.getSecretKey();

        ClientBuilderConfiguration configuration = new ClientBuilderConfiguration();
        configuration.setRequestTimeout(TIMEOUT);
        configuration.setConnectionRequestTimeout(TIMEOUT);
        OSS ossClient = new OSSClientBuilder().build(endPoint, accessKey, secretKey,configuration);
        return ossClient;
    }

    /**
     * 关闭 oss
     *
     * @param oss 客户端
     */
    public static void closeAmazonS3(OSS oss) {
        if (Objects.nonNull(oss)) {
            oss.shutdown();
        }
    }

    /**
     * 强转 sourceDTO 为 AwsS3SourceDTO
     *
     * @param sourceDTO aws s3 sourceDTO
     * @return 转换后的 aws s3 sourceDTO
     */
    public static OssSourceDTO convertSourceDTO(ISourceDTO sourceDTO) {
        if (!(sourceDTO instanceof OssSourceDTO)) {
            throw new DtLoaderException("please pass in OssSourceDTO...");
        }
        return (OssSourceDTO) sourceDTO;
    }
}
