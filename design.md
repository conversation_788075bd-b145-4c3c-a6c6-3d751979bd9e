# checkConnectionByDbAndSchema Method Technical Design

## 1. Architecture Overview

### 1.1 System Context
The `checkConnectionByDbAndSchema` method will be implemented within the existing `DatasourceUtils` class, leveraging the established patterns for database connectivity and validation. The method will integrate with the existing `IClient` framework to support multiple database types.

### 1.2 Design Principles
- **Consistency**: Follow existing patterns in `DatasourceUtils`
- **Extensibility**: Support all database types in `DataSourceTypeEnum`
- **Reliability**: Handle failures gracefully with appropriate error handling
- **Performance**: Optimize for connection validation speed and resource usage
- **Security**: Validate inputs and handle credentials securely

### 1.3 High-Level Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    checkConnectionByDbAndSchema               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ Input Validator │  │ Connection Pool │  │ Error Handler│ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           Database-Specific Validators                  │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐     │ │
│  │  │   RDBMS     │ │ DataWarehouse│ │     NoSQL       │ │     │ │
│  │  │ Validator   │ │ Validator   │ │    Validator    │ │     │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────┘     │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ Result Handler │  │ Resource Cleanup│  │ Logger      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 2. Method Design

### 2.1 Method Signature
```java
/**
 * 指定数据库和模式进行连接测试
 * @param datasourceDTO datasourceDTO containing dbName, schema, and connection details
 * @return true if connection to specified database and schema is successful, false otherwise
 */
public static Boolean checkConnectionByDbAndSchema(@NotNull DatasourceDTO datasourceDTO) {
    // Implementation
}
```

### 2.2 Core Algorithm
```java
public static Boolean checkConnectionByDbAndSchema(@NotNull DatasourceDTO datasourceDTO) {
    // 1. Input Validation
    validateInputParameters(datasourceDTO);
    
    // 2. Initialize plugin path if needed
    if (datasourceDTO.isInitDataSourcePluginPath()) {
        initDataSourcePluginPath(datasourceDTO.getDataSourcePluginPath());
    }
    
    // 3. Get database type enum
    DataSourceTypeEnum typeEnum = getDataSourceTypeEnum(datasourceDTO);
    
    // 4. Create source DTO
    ISourceDTO sourceDTO = createSourceDTO(datasourceDTO, typeEnum);
    
    // 5. Get client for database type
    IClient client = ClientCache.getClient(typeEnum.getVal());
    
    // 6. Perform database-specific validation
    return performDatabaseSpecificValidation(datasourceDTO, sourceDTO, client, typeEnum);
}
```

## 3. Database-Specific Validation Strategies

### 3.1 RDBMS Validation Strategy
```java
private static Boolean validateRdbmsConnection(DatasourceDTO datasourceDTO, 
                                                ISourceDTO sourceDTO, 
                                                IClient client, 
                                                DataSourceTypeEnum typeEnum) {
    try {
        // Test basic connection
        if (!client.testCon(sourceDTO)) {
            return false;
        }
        
        // Get database name from DTO
        String dbName = datasourceDTO.getDbName();
        String schema = datasourceDTO.getSchema();
        
        // Validate database exists
        if (!validateDatabaseExists(client, sourceDTO, dbName, typeEnum)) {
            return false;
        }
        
        // Validate schema exists (if applicable)
        if (StringUtils.isNotBlank(schema) && supportsSchema(typeEnum)) {
            return validateSchemaExists(client, sourceDTO, schema, typeEnum);
        }
        
        return true;
    } catch (Exception e) {
        log.error("RDBMS validation failed for database: {}, schema: {}", 
                 datasourceDTO.getDbName(), datasourceDTO.getSchema(), e);
        return false;
    }
}
```

### 3.2 Data Warehouse Validation Strategy
```java
private static Boolean validateDataWarehouseConnection(DatasourceDTO datasourceDTO, 
                                                      ISourceDTO sourceDTO, 
                                                      IClient client, 
                                                      DataSourceTypeEnum typeEnum) {
    try {
        // Test basic connection
        if (!client.testCon(sourceDTO)) {
            return false;
        }
        
        // Get all available databases/schemas
        List<String> availableDatabases = client.getAllDatabases(sourceDTO, 
            SqlQueryDTO.builder().build());
        
        // Check if requested database exists
        String targetDb = datasourceDTO.getDbName();
        if (!availableDatabases.contains(targetDb)) {
            log.warn("Database {} not found in available databases: {}", 
                    targetDb, availableDatabases);
            return false;
        }
        
        // For Hive/Spark, validate schema as namespace
        if (isHiveFamily(typeEnum) && StringUtils.isNotBlank(datasourceDTO.getSchema())) {
            return validateHiveNamespace(client, sourceDTO, datasourceDTO.getSchema());
        }
        
        return true;
    } catch (Exception e) {
        log.error("Data warehouse validation failed for database: {}, schema: {}", 
                 datasourceDTO.getDbName(), datasourceDTO.getSchema(), e);
        return false;
    }
}
```

### 3.3 NoSQL Validation Strategy
```java
private static Boolean validateNoSqlConnection(DatasourceDTO datasourceDTO, 
                                              ISourceDTO sourceDTO, 
                                              IClient client, 
                                              DataSourceTypeEnum typeEnum) {
    try {
        // Test basic connection
        if (!client.testCon(sourceDTO)) {
            return false;
        }
        
        // Handle HBase specific validation
        if (isHBase(typeEnum)) {
            return validateHBaseConnection(datasourceDTO, sourceDTO, typeEnum);
        }
        
        // Handle MongoDB specific validation
        if (isMongoDB(typeEnum)) {
            return validateMongoDBConnection(datasourceDTO, sourceDTO, typeEnum);
        }
        
        // Default: basic connection test is sufficient
        return true;
    } catch (Exception e) {
        log.error("NoSQL validation failed for database: {}, schema: {}", 
                 datasourceDTO.getDbName(), datasourceDTO.getSchema(), e);
        return false;
    }
}
```

## 4. Database Existence Validation

### 4.1 Database Existence Check
```java
private static Boolean validateDatabaseExists(IClient client, 
                                            ISourceDTO sourceDTO, 
                                            String dbName, 
                                            DataSourceTypeEnum typeEnum) {
    try {
        // Get list of available databases
        List<String> availableDatabases = client.getAllDatabases(sourceDTO, 
            SqlQueryDTO.builder().build());
        
        // Check if target database exists
        return availableDatabases.contains(dbName);
    } catch (Exception e) {
        log.error("Failed to validate database existence for: {}", dbName, e);
        return false;
    }
}
```

### 4.2 Schema Existence Check
```java
private static Boolean validateSchemaExists(IClient client, 
                                          ISourceDTO sourceDTO, 
                                          String schema, 
                                          DataSourceTypeEnum typeEnum) {
    try {
        // For databases that support schemas
        if (supportsSchema(typeEnum)) {
            // Execute schema-specific query
            String checkSchemaSql = getSchemaCheckSql(typeEnum, schema);
            SqlQueryDTO queryDTO = SqlQueryDTO.builder()
                .sql(checkSchemaSql)
                .build();
            
            List<Map<String, Object>> result = client.executeQuery(sourceDTO, queryDTO);
            return CollectionUtils.isNotEmpty(result);
        }
        
        // For databases that don't support schemas, consider it valid
        return true;
    } catch (Exception e) {
        log.error("Failed to validate schema existence for: {}", schema, e);
        return false;
    }
}
```

## 5. Database-Specific SQL Queries

### 5.1 Schema Check SQL Templates
```java
private static String getSchemaCheckSql(DataSourceTypeEnum typeEnum, String schema) {
    switch (typeEnum) {
        case Oracle:
            return String.format("SELECT 1 FROM ALL_USERS WHERE USERNAME = '%s'", schema);
        case PostgreSQL:
        case TBase:
        case DWS_PG:
        case ADB_PostgreSQL:
            return String.format("SELECT 1 FROM information_schema.schemata WHERE schema_name = '%s'", schema);
        case SQLServer:
        case SQLSERVER_2017_LATER:
            return String.format("SELECT 1 FROM information_schema.schemata WHERE schema_name = '%s'", schema);
        case DB2:
            return String.format("SELECT 1 FROM SYSCAT.SCHEMATA WHERE SCHEMANAME = '%s'", schema);
        case Hive1X:
        case Hive2X:
        case Hive3X:
            return String.format("SHOW DATABASES LIKE '%s'", schema);
        default:
            return String.format("SELECT 1 FROM information_schema.schemata WHERE schema_name = '%s'", schema);
    }
}
```

### 5.2 Database Check SQL Templates
```java
private static String getDatabaseCheckSql(DataSourceTypeEnum typeEnum, String dbName) {
    switch (typeEnum) {
        case MySQL:
        case MySQL8:
        case MariaDB:
        case TiDB:
            return String.format("SELECT 1 FROM information_schema.schemata WHERE schema_name = '%s'", dbName);
        case Oracle:
            return String.format("SELECT 1 FROM V$DATABASE WHERE NAME = '%s'", dbName);
        case SQLServer:
        case SQLSERVER_2017_LATER:
            return String.format("SELECT 1 FROM sys.databases WHERE name = '%s'", dbName);
        case PostgreSQL:
        case TBase:
        case DWS_PG:
        case ADB_PostgreSQL:
            return String.format("SELECT 1 FROM pg_database WHERE datname = '%s'", dbName);
        case DB2:
            return String.format("SELECT 1 FROM SYSCAT.DATABASES WHERE DBNAME = '%s'", dbName);
        default:
            return String.format("SELECT 1 FROM information_schema.schemata WHERE schema_name = '%s'", dbName);
    }
}
```

## 6. Helper Methods and Utilities

### 6.1 Database Type Classification
```java
private static boolean isRdbms(DataSourceTypeEnum typeEnum) {
    return Set.of(
        DataSourceTypeEnum.MySQL, DataSourceTypeEnum.MySQL8,
        DataSourceTypeEnum.Oracle, DataSourceTypeEnum.SQLServer,
        DataSourceTypeEnum.PostgreSQL, DataSourceTypeEnum.DB2,
        DataSourceTypeEnum.DMDB, DataSourceTypeEnum.KINGBASE8
    ).contains(typeEnum);
}

private static boolean isDataWarehouse(DataSourceTypeEnum typeEnum) {
    return Set.of(
        DataSourceTypeEnum.HIVE1X, DataSourceTypeEnum.HIVE2X,
        DataSourceTypeEnum.HIVE3X, DataSourceTypeEnum.SparkThrift2_1,
        DataSourceTypeEnum.MAXCOMPUTE, DataSourceTypeEnum.Impala,
        DataSourceTypeEnum.Greenplum6, DataSourceTypeEnum.ClickHouse
    ).contains(typeEnum);
}

private static boolean isNoSql(DataSourceTypeEnum typeEnum) {
    return Set.of(
        DataSourceTypeEnum.HBASE, DataSourceTypeEnum.HBASE2,
        DataSourceTypeEnum.MONGODB, DataSourceTypeEnum.REDIS
    ).contains(typeEnum);
}

private static boolean supportsSchema(DataSourceTypeEnum typeEnum) {
    return Set.of(
        DataSourceTypeEnum.Oracle, DataSourceTypeEnum.PostgreSQL,
        DataSourceTypeEnum.SQLServer, DataSourceTypeEnum.DB2,
        DataSourceTypeEnum.DMDB, DataSourceTypeEnum.KINGBASE8
    ).contains(typeEnum);
}
```

### 6.2 Input Validation
```java
private static void validateInputParameters(DatasourceDTO datasourceDTO) {
    if (datasourceDTO == null) {
        throw new IllegalArgumentException("DatasourceDTO cannot be null");
    }
    
    if (StringUtils.isBlank(datasourceDTO.getDbName())) {
        throw new IllegalArgumentException("Database name cannot be null or empty");
    }
    
    if (StringUtils.isBlank(datasourceDTO.getDataType())) {
        throw new IllegalArgumentException("Data type cannot be null or empty");
    }
    
    if (datasourceDTO.getDataJsonMap() == null) {
        throw new IllegalArgumentException("Data JSON map cannot be null");
    }
    
    // Validate data type is supported
    try {
        DataSourceTypeEnum.typeVersionOf(datasourceDTO.getDataType(), 
            datasourceDTO.getDataVersion() != null ? datasourceDTO.getDataVersion() : "");
    } catch (Exception e) {
        throw new IllegalArgumentException("Unsupported data type: " + datasourceDTO.getDataType(), e);
    }
}
```

## 7. Error Handling Design

### 7.1 Exception Handling Strategy
```java
private static Boolean handleValidationException(DatasourceDTO datasourceDTO, Exception e) {
    if (e instanceof SQLException) {
        log.error("SQL error validating connection to database: {}, schema: {}", 
                 datasourceDTO.getDbName(), datasourceDTO.getSchema(), e);
        return false;
    }
    
    if (e instanceof RdosDefineException) {
        log.error("Configuration error validating connection to database: {}, schema: {}", 
                 datasourceDTO.getDbName(), datasourceDTO.getSchema(), e);
        return false;
    }
    
    if (e instanceof IllegalArgumentException) {
        log.error("Invalid input parameters for database: {}, schema: {}", 
                 datasourceDTO.getDbName(), datasourceDTO.getSchema(), e);
        throw e;
    }
    
    // Generic error handling
    log.error("Unexpected error validating connection to database: {}, schema: {}", 
             datasourceDTO.getDbName(), datasourceDTO.getSchema(), e);
    return false;
}
```

### 7.2 Resource Management
```java
private static void cleanupResources(Connection connection, Statement statement, ResultSet resultSet) {
    try {
        if (resultSet != null) {
            resultSet.close();
        }
    } catch (SQLException e) {
        log.warn("Error closing ResultSet", e);
    }
    
    try {
        if (statement != null) {
            statement.close();
        }
    } catch (SQLException e) {
        log.warn("Error closing Statement", e);
    }
    
    try {
        if (connection != null) {
            connection.close();
        }
    } catch (SQLException e) {
        log.warn("Error closing Connection", e);
    }
}
```

## 8. Performance Optimization

### 8.1 Connection Pooling
```java
private static final Map<String, Object> connectionPoolConfig = Map.of(
    "maxTotal", 20,
    "maxIdle", 10,
    "minIdle", 5,
    "maxWaitMillis", 30000,
    "testOnBorrow", true,
    "testOnReturn", false,
    "testWhileIdle", true,
    "timeBetweenEvictionRunsMillis", 60000,
    "minEvictableIdleTimeMillis", 300000
);
```

### 8.2 Timeout Configuration
```java
private static final int CONNECTION_TIMEOUT = 30000; // 30 seconds
private static final int QUERY_TIMEOUT = 10000; // 10 seconds
private static final int VALIDATION_TIMEOUT = 15000; // 15 seconds
```

### 8.3 Caching Strategy
```java
private static final Cache<String, Boolean> validationCache = 
    Caffeine.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(5, TimeUnit.MINUTES)
        .build();

private static String getCacheKey(DatasourceDTO datasourceDTO) {
    return String.format("%s_%s_%s_%s", 
        datasourceDTO.getDataType(),
        datasourceDTO.getDataVersion(),
        datasourceDTO.getDbName(),
        datasourceDTO.getSchema());
}
```

## 9. Security Considerations

### 9.1 Input Sanitization
```java
private static String sanitizeDatabaseName(String dbName) {
    if (StringUtils.isBlank(dbName)) {
        return dbName;
    }
    
    // Remove potentially dangerous characters
    return dbName.replaceAll("[;'\"\\-\\-]", "");
}

private static String sanitizeSchemaName(String schema) {
    if (StringUtils.isBlank(schema)) {
        return schema;
    }
    
    // Remove potentially dangerous characters
    return schema.replaceAll("[;'\"\\-\\-]", "");
}
```

### 9.2 SQL Injection Prevention
```java
private static String createSafeSqlQuery(String template, String... params) {
    // Use parameterized queries where possible
    // For database-specific queries, use whitelisting and escaping
    String[] sanitizedParams = Arrays.stream(params)
        .map(DatasourceUtils::sanitizeDatabaseName)
        .toArray(String[]::new);
    
    return String.format(template, (Object[]) sanitizedParams);
}
```

## 10. Logging Strategy

### 10.1 Log Configuration
```java
private static final Logger validationLogger = LoggerFactory.getLogger("datasource.validation");

private static void logValidationStart(DatasourceDTO datasourceDTO) {
    if (log.isInfoEnabled()) {
        log.info("Starting connection validation for database: {}, schema: {}, type: {}", 
                datasourceDTO.getDbName(), 
                datasourceDTO.getSchema(), 
                datasourceDTO.getDataType());
    }
}

private static void logValidationSuccess(DatasourceDTO datasourceDTO, long durationMs) {
    if (log.isInfoEnabled()) {
        log.info("Connection validation successful for database: {}, schema: {}, type: {}, duration: {}ms", 
                datasourceDTO.getDbName(), 
                datasourceDTO.getSchema(), 
                datasourceDTO.getDataType(),
                durationMs);
    }
}

private static void logValidationFailure(DatasourceDTO datasourceDTO, String reason, long durationMs) {
    if (log.isWarnEnabled()) {
        log.warn("Connection validation failed for database: {}, schema: {}, type: {}, reason: {}, duration: {}ms", 
                datasourceDTO.getDbName(), 
                datasourceDTO.getSchema(), 
                datasourceDTO.getDataType(),
                reason,
                durationMs);
    }
}
```

## 11. Testing Strategy

### 11.1 Unit Test Design
```java
class CheckConnectionByDbAndSchemaTest {
    
    @Test
    void testValidateInputParameters_ValidInput() {
        DatasourceDTO dto = createValidDatasourceDTO();
        assertDoesNotThrow(() -> validateInputParameters(dto));
    }
    
    @Test
    void testValidateInputParameters_NullInput() {
        assertThrows(IllegalArgumentException.class, 
            () -> validateInputParameters(null));
    }
    
    @Test
    void testValidateInputParameters_EmptyDatabaseName() {
        DatasourceDTO dto = createValidDatasourceDTO();
        dto.setDbName("");
        assertThrows(IllegalArgumentException.class, 
            () -> validateInputParameters(dto));
    }
    
    @Test
    void testValidateInputParameters_UnsupportedDataType() {
        DatasourceDTO dto = createValidDatasourceDTO();
        dto.setDataType("UnsupportedDatabase");
        assertThrows(IllegalArgumentException.class, 
            () -> validateInputParameters(dto));
    }
}
```

### 11.2 Integration Test Design
```java
class CheckConnectionByDbAndSchemaIntegrationTest {
    
    @Test
    void testMySQLConnection_ValidDatabase() {
        DatasourceDTO dto = createMySQLDatasourceDTO("test_db", null);
        Boolean result = checkConnectionByDbAndSchema(dto);
        assertTrue(result);
    }
    
    @Test
    void testPostgreSQLConnection_ValidDatabaseAndSchema() {
        DatasourceDTO dto = createPostgreSQLDatasourceDTO("test_db", "public");
        Boolean result = checkConnectionByDbAndSchema(dto);
        assertTrue(result);
    }
    
    @Test
    void testConnection_InvalidDatabase() {
        DatasourceDTO dto = createMySQLDatasourceDTO("nonexistent_db", null);
        Boolean result = checkConnectionByDbAndSchema(dto);
        assertFalse(result);
    }
}
```

## 12. Deployment Considerations

### 12.1 Configuration
```java
// Application configuration
@Configuration
public class DatasourceValidationConfig {
    
    @Value("${datasource.validation.timeout:30000}")
    private int validationTimeout;
    
    @Value("${datasource.validation.cache.enabled:true}")
    private boolean cacheEnabled;
    
    @Value("${datasource.validation.cache.size:1000}")
    private int cacheSize;
    
    @Value("${datasource.validation.cache.expiry:300}")
    private int cacheExpirySeconds;
    
    @Bean
    public Cache<String, Boolean> validationCache() {
        if (!cacheEnabled) {
            return Caffeine.newBuilder().maximumSize(0).build();
        }
        
        return Caffeine.newBuilder()
            .maximumSize(cacheSize)
            .expireAfterWrite(cacheExpirySeconds, TimeUnit.SECONDS)
            .build();
    }
}
```

### 12.2 Monitoring
```java
@Component
public class DatasourceValidationMetrics {
    
    private final Counter validationSuccessCounter;
    private final Counter validationFailureCounter;
    private final Timer validationTimer;
    
    public DatasourceValidationMetrics(MeterRegistry meterRegistry) {
        this.validationSuccessCounter = Counter.builder("datasource.validation.success")
            .description("Number of successful datasource validations")
            .register(meterRegistry);
            
        this.validationFailureCounter = Counter.builder("datasource.validation.failure")
            .description("Number of failed datasource validations")
            .register(meterRegistry);
            
        this.validationTimer = Timer.builder("datasource.validation.time")
            .description("Time taken for datasource validation")
            .register(meterRegistry);
    }
    
    public void recordSuccess(String dataType, String dbName, String schema) {
        validationSuccessCounter.increment(
            Tags.of("dataType", dataType, "dbName", dbName, "schema", schema));
    }
    
    public void recordFailure(String dataType, String dbName, String schema, String error) {
        validationFailureCounter.increment(
            Tags.of("dataType", dataType, "dbName", dbName, "schema", schema, "error", error));
    }
    
    public Timer.Sample startTimer() {
        return Timer.start();
    }
}
```

## 13. Future Enhancements

### 13.1 Extensibility Points
- **Plugin Architecture**: Allow custom validation strategies for new database types
- **Configuration-Driven**: Enable validation rules to be configured externally
- **Async Support**: Add asynchronous validation methods for better performance
- **Batch Validation**: Support validating multiple database/schema combinations

### 13.2 Monitoring and Observability
- **Detailed Metrics**: Add more granular performance metrics
- **Distributed Tracing**: Integrate with tracing systems
- **Health Checks**: Provide health check endpoints
- **Alerting**: Implement alerting for validation failures

### 13.3 Performance Improvements
- **Connection Reuse**: Implement connection reuse strategies
- **Parallel Validation**: Support parallel validation of multiple databases
- **Adaptive Timeout**: Implement adaptive timeout based on database type
- **Result Caching**: Enhance caching strategies for better performance