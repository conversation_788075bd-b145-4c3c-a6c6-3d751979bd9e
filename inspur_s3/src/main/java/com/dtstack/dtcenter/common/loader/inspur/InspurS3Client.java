/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.inspur;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.Bucket;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.dsg.database.datasource.dto.Base64DatasourceJson;
import com.dsg.database.datasource.dto.DatasourceInfoDTO;
import com.dsg.database.datasource.dto.DatasourceInfoImportVO;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.dsg.database.datasource.utils.ParseDatasourceUtils;
import com.dtstack.dtcenter.common.loader.common.nosql.AbsNoSqlClient;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.InspurS3SourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * aws s3 Client
 *
 * <AUTHOR>
 * date：Created in 上午9:46 2021/5/6
 * company: www.dtstack.com
 */
@Slf4j
public class InspurS3Client<T> extends AbsNoSqlClient<T> {

    /**
     * s3 object 前置查询需要以 .* 结尾
     */
    private static final String SEARCH_PREFIX_SING = ".*";

    @Override
    public Boolean testCon(ISourceDTO source) {
        InspurS3SourceDTO sourceDTO = InspurS3Util.convertSourceDTO(source);
        AmazonS3 amazonS3 = null;
        try {
            amazonS3 = InspurS3Util.getClient(sourceDTO);
            amazonS3.listBuckets();
        } catch (Exception e) {
            throw new DtLoaderException(String.format("inspur s3 connection failed : %s", e.getMessage()), e);
        } finally {
            InspurS3Util.closeAmazonS3(amazonS3);
        }
        return true;
    }

    @Override
    public List<String> getTableList(ISourceDTO source, SqlQueryDTO queryDTO) {
        InspurS3SourceDTO sourceDTO = InspurS3Util.convertSourceDTO(source);
        String bucket = queryDTO.getSchema();
        if (StringUtils.isBlank(bucket)) {
            throw new DtLoaderException("bucket cannot be blank....");
        }
        String tableNamePattern = queryDTO.getTableNamePattern();
        // 是否匹配查询
        boolean isPattern = StringUtils.isNotBlank(tableNamePattern);
        // 仅支持前置匹配
        boolean isPrefix = isPattern && tableNamePattern.endsWith(SEARCH_PREFIX_SING);
        AmazonS3 amazonS3 = null;
        List<String> objectList;
        try {
            amazonS3 = InspurS3Util.getClient(sourceDTO);
            ObjectListing objectListing;
            if (!isPattern) {
                objectListing = amazonS3.listObjects(bucket);
            } else {
                objectListing = amazonS3.listObjects(bucket, isPrefix ? tableNamePattern.substring(0, tableNamePattern.length() - 2) : tableNamePattern);
            }
            if (Objects.isNull(objectListing)) {
                return Lists.newArrayList();
            }
            List<S3ObjectSummary> objectSummaries = objectListing.getObjectSummaries();
            if (CollectionUtils.isEmpty(objectSummaries)) {
                return Lists.newArrayList();
            }
            objectList = objectSummaries.stream().map(S3ObjectSummary::getKey).collect(Collectors.toList());
        } catch (Exception e) {
            throw new DtLoaderException(String.format("inspur s3 get buckets failed : %s", e.getMessage()), e);
        } finally {
            InspurS3Util.closeAmazonS3(amazonS3);
        }
        if (isPattern && !isPrefix) {
            objectList = objectList.stream().filter(table -> StringUtils.equalsIgnoreCase(table, tableNamePattern)).collect(Collectors.toList());
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            objectList = objectList.stream().limit(queryDTO.getLimit()).collect(Collectors.toList());
        }
        return objectList;
    }

    @Override
    public List<String> getTableListBySchema(ISourceDTO source, SqlQueryDTO queryDTO) {
        return getTableList(source, queryDTO);
    }

    @Override
    public List<String> getAllDatabases(ISourceDTO source, SqlQueryDTO queryDTO) {
        InspurS3SourceDTO sourceDTO = InspurS3Util.convertSourceDTO(source);
        AmazonS3 amazonS3 = null;
        List<String> result;
        try {
            amazonS3 = InspurS3Util.getClient(sourceDTO);
            List<Bucket> buckets = amazonS3.listBuckets();
            result = buckets.stream().map(Bucket::getName).collect(Collectors.toList());
        } catch (Exception e) {
            throw new DtLoaderException(String.format("inspur s3 get buckets failed : %s", e.getMessage()), e);
        } finally {
            InspurS3Util.closeAmazonS3(amazonS3);
        }
        if (StringUtils.isNotBlank(queryDTO.getSchema())) {
            result = result.stream().filter(bucket -> StringUtils.containsIgnoreCase(bucket, queryDTO.getSchema().trim())).collect(Collectors.toList());
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            result = result.stream().limit(queryDTO.getLimit()).collect(Collectors.toList());
        }
        return result;
    }


    /**
     * 获取数据源动态导入
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    @Override
    public DatasourceInfoDTO getDataSourceImport(ISourceDTO source, SqlQueryDTO queryDTO){
        //获取导入对象
        DatasourceInfoImportVO datasourceInfoImportVO = queryDTO.getDatasourceInfoImportVO();
        //ftp 或者sftp 导入

        //获取枚举
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.typeVersionOf(datasourceInfoImportVO.getDataType(), datasourceInfoImportVO.getDataVersion());
        //构建加密字符串
        Base64DatasourceJson base64DatasourceJson = new Base64DatasourceJson();
        base64DatasourceJson.setDataName(datasourceInfoImportVO.getDataName());
        base64DatasourceJson.setDataTypeCode(dataSourceTypeEnum.getVal());
        base64DatasourceJson.setBusinessUuid(queryDTO.getBusinessUuid());
        base64DatasourceJson.setDataType(dataSourceTypeEnum.getDataType());
        base64DatasourceJson.setShowDataType(dataSourceTypeEnum.getDataType());
        base64DatasourceJson.setDriverClassName(dataSourceTypeEnum.getDriverClassName());
        base64DatasourceJson.setDataVersion(dataSourceTypeEnum.getDataVersion());
        base64DatasourceJson.setSchema(datasourceInfoImportVO.getSchema());
        String userName = datasourceInfoImportVO.getUserName();
        String passWord = datasourceInfoImportVO.getPassWord();
        base64DatasourceJson.setUsername(userName);
        base64DatasourceJson.setPassword(passWord);
        base64DatasourceJson.setHost(datasourceInfoImportVO.getIp());
        base64DatasourceJson.setPort(datasourceInfoImportVO.getPort());
        base64DatasourceJson.setAccessKey(datasourceInfoImportVO.getAccessKey());
        base64DatasourceJson.setSecretKey(datasourceInfoImportVO.getSecretKey());
        base64DatasourceJson.setBucket(datasourceInfoImportVO.getBucket());
        base64DatasourceJson.setRegion(datasourceInfoImportVO.getRegion());
        base64DatasourceJson.setEndPoint(datasourceInfoImportVO.getEndPoint());

        //设置数据源类型编码
        InspurS3SourceDTO sourceDTO = InspurS3Util.convertSourceDTO(source);
        sourceDTO.setAccessKey(datasourceInfoImportVO.getAccessKey());
        sourceDTO.setSecretKey(datasourceInfoImportVO.getSecretKey());
        sourceDTO.setEndPoint(datasourceInfoImportVO.getEndPoint());
        sourceDTO.setRegion(datasourceInfoImportVO.getRegion());

        Integer status = 0;
        try {
            //测试连接
            Boolean b = testCon(sourceDTO);
            if (b) {
                //获取版本
                String version = "";
                try {
                    version = getVersion(sourceDTO);
                } catch (Exception e) {
                    log.error("获取版本失败{}",datasourceInfoImportVO.getDataName());
                }
                base64DatasourceJson.setDbVersion(version);
                status=1;
            }
        } catch (Exception e) {
            log.error("[{}]数据源连接失败",datasourceInfoImportVO.getDataName());

        }
        //构建数据源对象
        String dataJson = JSONObject.toJSONString(base64DatasourceJson);
        //将json 转为对象
        DatasourceInfoDTO dto = JSON.parseObject(dataJson, DatasourceInfoDTO.class);
        dto.setStatus(status);
        //加密字符串
        //将桶名称改为大写
        JSONObject jsonObject = JSONObject.parseObject(dataJson);
        jsonObject.put("Bucket",datasourceInfoImportVO.getBucket());
        dto.setDataJson(ParseDatasourceUtils.getEncodeDataSource(jsonObject.toJSONString(), true));
        dto.setDbName(datasourceInfoImportVO.getDbName());

        String dataDesc = datasourceInfoImportVO.getDataDesc();
        if(StringUtils.isNotEmpty(dataDesc)){
            dto.setDataDesc(dataDesc);
        }
        dto.setIp(datasourceInfoImportVO.getIp());
        return dto;
    }

}
