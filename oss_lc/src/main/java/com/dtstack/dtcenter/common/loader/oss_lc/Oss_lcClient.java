/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.oss_lc;

import com.dtstack.dtcenter.common.loader.common.nosql.AbsNoSqlClient;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.OssLcSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.google.common.collect.Lists;
import com.inspurcloud.oss.client.OSSClient;
import com.inspurcloud.oss.model.entity.ObjectListing;
import com.inspurcloud.oss.model.entity.ObjectSummary;
import com.inspurcloud.oss.model.entity.SimpleBucketInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * aws s3 Client
 *
 * <AUTHOR>
 * date：Created in 上午9:46 2021/5/6
 * company: www.dtstack.com
 */
public class Oss_lcClient<T> extends AbsNoSqlClient<T> {

    /**
     * s3 object 前置查询需要以 .* 结尾
     */
    private static final String SEARCH_PREFIX_SING = ".*";

    @Override
    public Boolean testCon(ISourceDTO source) {
        OssLcSourceDTO sourceDTO = Oss_lcUtil.convertSourceDTO(source);
        OSSClient oss = null;
        try {
            oss = Oss_lcUtil.getClient(sourceDTO);
            oss.listAllBucket();
        } catch (Exception e) {
            throw new DtLoaderException(String.format("oss_lc connection failed : %s", e.getMessage()), e);
        } finally {
            Oss_lcUtil.closeAmazonS3(oss);
        }
        return true;
    }

    @Override
    public List<String> getTableList(ISourceDTO source, SqlQueryDTO queryDTO) {
        OssLcSourceDTO sourceDTO = Oss_lcUtil.convertSourceDTO(source);
        String bucket = queryDTO.getSchema();
        if (StringUtils.isBlank(bucket)) {
            throw new DtLoaderException("bucket cannot be blank....");
        }
        String tableNamePattern = queryDTO.getTableNamePattern();
        // 是否匹配查询
        boolean isPattern = StringUtils.isNotBlank(tableNamePattern);
        // 仅支持前置匹配
        boolean isPrefix = isPattern && tableNamePattern.endsWith(SEARCH_PREFIX_SING);
        OSSClient oss = null;
        List<String> objectList = new ArrayList<>();
        try {
            oss = Oss_lcUtil.getClient(sourceDTO);
            ObjectListing objectListing;
            if (!isPattern) {
                objectListing = oss.listObjects(bucket);
            } else {
                objectListing = oss.listObjects(bucket, isPrefix ? tableNamePattern.substring(0, tableNamePattern.length() - 2) : tableNamePattern);
            }
            if (Objects.isNull(objectListing)) {
                return Lists.newArrayList();
            }
            List<ObjectSummary> objectSummaries = objectListing.getObjectSummaries();
            if (CollectionUtils.isEmpty(objectSummaries)) {
                return Lists.newArrayList();
            }
            for (ObjectSummary objectSummary : objectSummaries) {
                String key = objectSummary.getKey();
                objectList.add(key);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("oss_lc get buckets failed : %s", e.getMessage()), e);
        } finally {
            Oss_lcUtil.closeAmazonS3(oss);
        }
        if (isPattern && !isPrefix) {
            objectList = objectList.stream().filter(table -> StringUtils.equalsIgnoreCase(table, tableNamePattern)).collect(Collectors.toList());
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            objectList = objectList.stream().limit(queryDTO.getLimit()).collect(Collectors.toList());
        }
        return objectList;
    }

    @Override
    public List<String> getTableListBySchema(ISourceDTO source, SqlQueryDTO queryDTO) {
        return getTableList(source, queryDTO);
    }

    @Override
    public List<String> getAllDatabases(ISourceDTO source, SqlQueryDTO queryDTO) {
        OssLcSourceDTO sourceDTO = Oss_lcUtil.convertSourceDTO(source);
        OSSClient oss = null;
        List<String> result;
        try {
            oss = Oss_lcUtil.getClient(sourceDTO);
            List<SimpleBucketInfo> simpleBucketInfos = oss.listAllBucket();
            result = simpleBucketInfos.stream().map(SimpleBucketInfo::getBucketName).collect(Collectors.toList());
        } catch (Exception e) {
            throw new DtLoaderException(String.format("oss_lc get buckets failed : %s", e.getMessage()), e);
        } finally {
            Oss_lcUtil.closeAmazonS3(oss);
        }
        if (StringUtils.isNotBlank(queryDTO.getSchema())) {
            result = result.stream().filter(bucket -> StringUtils.containsIgnoreCase(bucket, queryDTO.getSchema().trim())).collect(Collectors.toList());
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            result = result.stream().limit(queryDTO.getLimit()).collect(Collectors.toList());
        }
        return result;
    }
}
