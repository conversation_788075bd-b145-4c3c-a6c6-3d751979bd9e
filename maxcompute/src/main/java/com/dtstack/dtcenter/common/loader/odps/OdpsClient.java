/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.odps;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.odps.Column;
import com.aliyun.odps.Instance;
import com.aliyun.odps.Odps;
import com.aliyun.odps.OdpsException;
import com.aliyun.odps.OdpsType;
import com.aliyun.odps.PartitionSpec;
import com.aliyun.odps.Table;
import com.aliyun.odps.TableSchema;
import com.aliyun.odps.Tables;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;
import com.aliyun.odps.data.DefaultRecordReader;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.data.ResultSet;
import com.aliyun.odps.task.SQLTask;
import com.dtstack.dtcenter.common.loader.common.exception.IErrorPattern;
import com.dtstack.dtcenter.common.loader.common.nosql.AbsNoSqlClient;
import com.dtstack.dtcenter.common.loader.common.service.ErrorAdapterImpl;
import com.dtstack.dtcenter.common.loader.common.service.IErrorAdapter;
import com.dtstack.dtcenter.common.loader.common.utils.CollectionUtil;
import com.dtstack.dtcenter.common.loader.common.utils.SearchUtil;
import com.dtstack.dtcenter.common.loader.odps.common.OdpsFields;
import com.dtstack.dtcenter.common.loader.odps.pool.OdpsManager;
import com.dtstack.dtcenter.common.loader.odps.pool.OdpsPool;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.DsIndexDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.TableViewDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.OdpsSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.enums.ConnectionClearStatus;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 17:47 2020/1/7
 * @Description：ODPS 客户端
 */
@Slf4j
public class OdpsClient<T> extends AbsNoSqlClient<T> {

    public static final ThreadLocal<Boolean> IS_OPEN_POOL = new ThreadLocal<>();

    private static OdpsManager odpsManager = OdpsManager.getInstance();

    private static final IErrorPattern ERROR_PATTERN = new OdpsErrorPattern();

    // 异常适配器
    private static final IErrorAdapter ERROR_ADAPTER = new ErrorAdapterImpl();

    private static final String DRIVER_NAME = "com.aliyun.odps.jdbc.OdpsDriver";

    @Override
    public Boolean testCon(ISourceDTO iSource) {
        OdpsSourceDTO odpsSourceDTO = (OdpsSourceDTO) iSource;
        Odps odps = null;
        try {
            odps = initOdps(odpsSourceDTO);
            Tables tables = odps.tables();
            tables.iterator().hasNext();
            return true;
        } catch (Exception ex) {
            throw new DtLoaderException(ERROR_ADAPTER.connAdapter(ex.getMessage(), ERROR_PATTERN), ex);
        } finally {
            closeResource(odps, odpsSourceDTO);
        }
    }

    @Override
    public Connection getCon(ISourceDTO source) {
        OdpsSourceDTO odpsSourceDTO = (OdpsSourceDTO) source;
        JSONObject odpsConfig = JSON.parseObject(odpsSourceDTO.getConfig());
        try {
            Class.forName(DRIVER_NAME);
            DriverManager.setLoginTimeout(15);
            return DriverManager.getConnection("jdbc:odps:" + odpsConfig.getString(OdpsFields.KEY_ODPS_SERVER) + "?project=" + odpsConfig.getString(OdpsFields.KEY_PROJECT),
                    odpsConfig.getString(OdpsFields.KEY_ACCESS_ID), odpsConfig.getString(OdpsFields.KEY_ACCESS_KEY));
        } catch (ClassNotFoundException | SQLException e) {
            throw new DtLoaderException(ERROR_ADAPTER.connAdapter(e.getMessage(), ERROR_PATTERN), e);
        }
    }

    public static Odps initOdps(OdpsSourceDTO odpsSourceDTO) {
        JSONObject odpsConfig = JSON.parseObject(odpsSourceDTO.getConfig());

        boolean check = odpsSourceDTO.getPoolConfig() != null;
        IS_OPEN_POOL.set(check);
        //不开启连接池
        if (!check) {
            return initOdps(odpsConfig.getString(OdpsFields.KEY_ODPS_SERVER), odpsConfig.getString(OdpsFields.KEY_ACCESS_ID),
                    odpsConfig.getString(OdpsFields.KEY_ACCESS_KEY), odpsConfig.getString(OdpsFields.KEY_PROJECT),
                    odpsConfig.getString(OdpsFields.PACKAGE_AUTHORIZED_PROJECT), odpsConfig.getString(OdpsFields.KEY_ACCOUNT_TYPE));
        }
        //开启连接池
        OdpsPool odpsPool = odpsManager.getConnection(odpsSourceDTO);
        Odps odps = odpsPool.getResource();
        if (Objects.isNull(odps)) {
            throw new DtLoaderException("No database connection available");
        }
        return odps;
    }

    public static Odps initOdps(String odpsServer, String accessId, String accessKey, String project,
                                String packageAuthorizedProject, String accountType) {
        if (StringUtils.isBlank(odpsServer)) {
            odpsServer = OdpsFields.DEFAULT_ODPS_SERVER;
        }

        if (StringUtils.isBlank(accessId)) {
            throw new IllegalArgumentException("accessId is required");
        }

        if (StringUtils.isBlank(accessKey)) {
            throw new IllegalArgumentException("accessKey is required");
        }

        if (StringUtils.isBlank(project)) {
            throw new IllegalArgumentException("project is required");
        }

        String defaultProject;
        if (StringUtils.isBlank(packageAuthorizedProject)) {
            defaultProject = project;
        } else {
            defaultProject = packageAuthorizedProject;
        }

        if (StringUtils.isBlank(accountType)) {
            accountType = OdpsFields.DEFAULT_ACCOUNT_TYPE;
        }

        Account account = null;
        if (accountType.equalsIgnoreCase(OdpsFields.DEFAULT_ACCOUNT_TYPE)) {
            account = new AliyunAccount(accessId, accessKey);
        } else {
            throw new IllegalArgumentException("Unsupported account type: " + accountType);
        }

        Odps odps = new Odps(account);
        odps.getRestClient().setConnectTimeout(3);
        odps.getRestClient().setReadTimeout(10);
        odps.getRestClient().setRetryTimes(2);
        odps.setDefaultProject(defaultProject);
        odps.setEndpoint(odpsServer);
        return odps;
    }

    @Override
    public List<String> getTableList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        OdpsSourceDTO odpsSourceDTO = (OdpsSourceDTO) iSource;
        beforeQuery(odpsSourceDTO, queryDTO, false);
        List<String> tableList = new ArrayList<>();
        Odps odps = null;
        try {
            odps = initOdps(odpsSourceDTO);
            odps.tables().forEach((Table table) -> tableList.add(table.getName()));
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            closeResource(odps, odpsSourceDTO);
        }
        return SearchUtil.handleSearchAndLimit(tableList, queryDTO);
    }

    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        log.debug("Starting getColumnMetaData for table: {}", queryDTO != null ? queryDTO.getTableName() : "null");
        
        try {
            // Validate input parameters
            validateGetColumnMetaDataInput(iSource, queryDTO);
            
            OdpsSourceDTO odpsSourceDTO = (OdpsSourceDTO) iSource;
            JSONObject odpsConfig = JSON.parseObject(odpsSourceDTO.getConfig());
            
            // Get basic column metadata from DatabaseMetaData
            Set<ColumnMetaDTO> columnMetadataSet = getBasicColumnMetadata(odpsSourceDTO, queryDTO, odpsConfig);
            
            // Enhance metadata with precision and type information
            List<ColumnMetaDTO> enhancedColumns = enhanceColumnMetadata(
                odpsSourceDTO, queryDTO, odpsConfig, columnMetadataSet);
            
            log.debug("Successfully retrieved metadata for {} columns", enhancedColumns.size());
            return enhancedColumns;
            
        } catch (DtLoaderException e) {
            throw e;
        } catch (Exception e) {
            String errorMsg = String.format("Failed to get column metadata for table %s: %s", 
                queryDTO != null ? queryDTO.getTableName() : "unknown", e.getMessage());
            log.error(errorMsg, e);
            throw new DtLoaderException(errorMsg, e);
        }
    }

    /**
     * Validates input parameters for getColumnMetaData method
     */
    private void validateGetColumnMetaDataInput(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        if (iSource == null) {
            throw new DtLoaderException("Source DTO cannot be null");
        }
        
        if (!(iSource instanceof OdpsSourceDTO)) {
            throw new DtLoaderException("Source DTO must be of type OdpsSourceDTO");
        }
        
        if (queryDTO == null) {
            throw new DtLoaderException("Query DTO cannot be null");
        }
        
        if (StringUtils.isBlank(queryDTO.getTableName())) {
            throw new DtLoaderException("Table name cannot be null or empty");
        }
        
        OdpsSourceDTO odpsSourceDTO = (OdpsSourceDTO) iSource;
        if (StringUtils.isBlank(odpsSourceDTO.getConfig())) {
            throw new DtLoaderException("ODPS configuration cannot be null or empty");
        }
        
        log.debug("Input validation passed for table: {}", queryDTO.getTableName());
    }

    /**
     * Retrieves basic column metadata from DatabaseMetaData including primary keys, foreign keys, and unique constraints
     */
    private Set<ColumnMetaDTO> getBasicColumnMetadata(OdpsSourceDTO odpsSourceDTO, SqlQueryDTO queryDTO, JSONObject odpsConfig) {
        log.debug("Retrieving basic column metadata for table: {}", queryDTO.getTableName());
        
        try (Connection connection = getCon(odpsSourceDTO)) {
            String catalog = determineCatalog(connection, queryDTO);
            DatabaseMetaData metaData = connection.getMetaData();
            
            // Retrieve constraint information
            Set<String> primaryKeys = getPrimaryKeys(metaData, catalog, queryDTO);
            Set<String> foreignKeys = getForeignKeys(metaData, catalog, queryDTO);
            Set<String> uniqueConstraints = getUniqueConstraints(metaData, catalog, queryDTO);
            
            log.debug("Retrieved constraints - PrimaryKeys: {}, ForeignKeys: {}, UniqueConstraints: {}", 
                primaryKeys.size(), foreignKeys.size(), uniqueConstraints.size());
            
            // Get column metadata
            return getColumnMetadata(metaData, catalog, odpsSourceDTO, queryDTO, 
                primaryKeys, foreignKeys, uniqueConstraints);
            
        } catch (SQLException e) {
            String errorMsg = String.format("Failed to get basic column metadata for table %s: %s", 
                queryDTO.getTableName(), e.getMessage());
            log.error(errorMsg, e);
            throw new DtLoaderException(errorMsg, e);
        }
    }

    /**
     * Determines the catalog name to use for metadata queries
     */
    private String determineCatalog(Connection connection, SqlQueryDTO queryDTO) throws SQLException {
        String catalog = connection.getCatalog();
        if (StringUtils.isNotEmpty(queryDTO.getSchema())) {
            catalog = queryDTO.getSchema();
        }
        log.debug("Using catalog: {}", catalog);
        return catalog;
    }

    /**
     * Retrieves primary key information for the table
     */
    private Set<String> getPrimaryKeys(DatabaseMetaData metaData, String catalog, SqlQueryDTO queryDTO) {
        Set<String> primaryKeys = new HashSet<>();
        
        try (java.sql.ResultSet pkRs = metaData.getPrimaryKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName())) {
            while (pkRs.next()) {
                String columnName = pkRs.getString("COLUMN_NAME");
                if (StringUtils.isNotBlank(columnName)) {
                    primaryKeys.add(columnName);
                }
            }
            log.debug("Found {} primary keys", primaryKeys.size());
        } catch (SQLException e) {
            log.warn("Failed to retrieve primary keys for table {}: {}", queryDTO.getTableName(), e.getMessage());
        }
        
        return primaryKeys;
    }

    /**
     * Retrieves foreign key information for the table
     */
    private Set<String> getForeignKeys(DatabaseMetaData metaData, String catalog, SqlQueryDTO queryDTO) {
        Set<String> foreignKeys = new HashSet<>();
        
        try (java.sql.ResultSet fkRs = metaData.getExportedKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName())) {
            while (fkRs.next()) {
                String columnName = fkRs.getString("PKCOLUMN_NAME");
                if (StringUtils.isNotBlank(columnName)) {
                    foreignKeys.add(columnName);
                }
            }
            log.debug("Found {} foreign keys", foreignKeys.size());
        } catch (SQLException e) {
            log.warn("Failed to retrieve foreign keys for table {}: {}", queryDTO.getTableName(), e.getMessage());
        }
        
        return foreignKeys;
    }

    /**
     * Retrieves unique constraint information for the table
     */
    private Set<String> getUniqueConstraints(DatabaseMetaData metaData, String catalog, SqlQueryDTO queryDTO) {
        Set<String> uniqueConstraints = new HashSet<>();
        
        try (java.sql.ResultSet uniqueRs = metaData.getIndexInfo(catalog, queryDTO.getSchema(), queryDTO.getTableName(), true, false)) {
            while (uniqueRs.next()) {
                String columnName = uniqueRs.getString("COLUMN_NAME");
                if (StringUtils.isNotBlank(columnName)) {
                    boolean nonUnique = uniqueRs.getBoolean("NON_UNIQUE");
                    if (!nonUnique) {
                        uniqueConstraints.add(columnName);
                    }
                }
            }
            log.debug("Found {} unique constraints", uniqueConstraints.size());
        } catch (SQLException e) {
            log.warn("Failed to retrieve unique constraints for table {}: {}", queryDTO.getTableName(), e.getMessage());
        }
        
        return uniqueConstraints;
    }

    /**
     * Retrieves column metadata from DatabaseMetaData and applies constraint information
     */
    private Set<ColumnMetaDTO> getColumnMetadata(DatabaseMetaData metaData, String catalog, 
            OdpsSourceDTO odpsSourceDTO, SqlQueryDTO queryDTO, Set<String> primaryKeys, 
            Set<String> foreignKeys, Set<String> uniqueConstraints) {
        
        Set<ColumnMetaDTO> columns = new LinkedHashSet<>();
        
        try (java.sql.ResultSet rsColumn = metaData.getColumns(catalog, odpsSourceDTO.getSchema(), queryDTO.getTableName(), null)) {
            while (rsColumn.next()) {
                ColumnMetaDTO columnMetaDTO = createColumnMetaDTO(rsColumn, primaryKeys, foreignKeys, uniqueConstraints);
                columns.add(columnMetaDTO);
            }
            log.debug("Processed {} columns from metadata", columns.size());
        } catch (SQLException e) {
            String errorMsg = String.format("Failed to retrieve column metadata for table %s: %s", 
                queryDTO.getTableName(), e.getMessage());
            log.error(errorMsg, e);
            throw new DtLoaderException(errorMsg, e);
        }
        
        return columns;
    }

    /**
     * Creates a ColumnMetaDTO from ResultSet row
     */
    private ColumnMetaDTO createColumnMetaDTO(java.sql.ResultSet rsColumn, Set<String> primaryKeys,
            Set<String> foreignKeys, Set<String> uniqueConstraints) throws SQLException {
        
        ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
        String columnName = rsColumn.getString("COLUMN_NAME");
        
        columnMetaDTO.setPart(false);
        columnMetaDTO.setKey(columnName);
        columnMetaDTO.setType(rsColumn.getString("TYPE_NAME"));
        columnMetaDTO.setComment(rsColumn.getString("REMARKS"));
        columnMetaDTO.setScale(getSafeInt(rsColumn, "DECIMAL_DIGITS"));
        columnMetaDTO.setLength(getSafeInt(rsColumn, "COLUMN_SIZE"));
        columnMetaDTO.setDataType(getSafeInt(rsColumn, "DATA_TYPE"));
        columnMetaDTO.setDefaultValue(rsColumn.getString("COLUMN_DEF"));
        columnMetaDTO.setNotNullFlag("NO".equalsIgnoreCase(rsColumn.getString("IS_NULLABLE")));
        
        // Apply constraint information
        columnMetaDTO.setPkflag(primaryKeys.contains(columnName));
        columnMetaDTO.setFkflag(foreignKeys.contains(columnName));
        columnMetaDTO.setUniqueFlag(uniqueConstraints.contains(columnName));
        
        log.debug("Created metadata for column: {}", columnName);
        return columnMetaDTO;
    }

    /**
     * Safely gets integer value from ResultSet, handling NULL values
     */
    private int getSafeInt(java.sql.ResultSet rs, String columnName) throws SQLException {
        int value = rs.getInt(columnName);
        return rs.wasNull() ? 0 : value;
    }

    /**
     * Enhances column metadata with precision and type information from actual query
     */
    private List<ColumnMetaDTO> enhanceColumnMetadata(OdpsSourceDTO odpsSourceDTO, SqlQueryDTO queryDTO, 
            JSONObject odpsConfig, Set<ColumnMetaDTO> basicColumns) {
        
        if (CollectionUtils.isEmpty(basicColumns)) {
            log.warn("No basic columns to enhance for table: {}", queryDTO.getTableName());
            return new ArrayList<>();
        }
        
        log.debug("Enhancing metadata for {} columns", basicColumns.size());
        
        try (Connection connection = getCon(odpsSourceDTO);
             Statement statement = connection.createStatement()) {
            
            // Build and execute query to get enhanced metadata
            String queryColumnSql = buildMetadataQuery(odpsConfig, queryDTO);
            log.debug("Executing metadata query: {}", queryColumnSql);
            
            try (java.sql.ResultSet rs = statement.executeQuery(queryColumnSql)) {
                ResultSetMetaData rsMetaData = rs.getMetaData();
                return processEnhancedMetadata(basicColumns, rsMetaData);
            }
            
        } catch (SQLException e) {
            String errorMsg = String.format("Failed to enhance column metadata for table %s: %s", 
                queryDTO.getTableName(), e.getMessage());
            log.error(errorMsg, e);
            throw new DtLoaderException(errorMsg, e);
        }
    }

    /**
     * Builds SQL query for metadata enhancement
     */
    private String buildMetadataQuery(JSONObject odpsConfig, SqlQueryDTO queryDTO) {
        String schema = StringUtils.isEmpty(queryDTO.getSchema()) ?
                odpsConfig.getString(OdpsFields.KEY_PROJECT) : queryDTO.getSchema();
        String columnList = "*";
        return String.format("SELECT %s FROM %s.%s WHERE 1=2", columnList, schema, queryDTO.getTableName());
    }

    /**
     * Processes enhanced metadata from ResultSetMetaData
     */
    private List<ColumnMetaDTO> processEnhancedMetadata(Set<ColumnMetaDTO> basicColumns, ResultSetMetaData rsMetaData) throws SQLException {
        List<ColumnMetaDTO> enhancedColumns = new ArrayList<>();
        int columnCount = rsMetaData.getColumnCount();
        
        for (int i = 1; i <= columnCount; i++) {
            String columnName = rsMetaData.getColumnName(i);
            
            // Find matching basic column metadata
            Optional<ColumnMetaDTO> matchingColumn = basicColumns.stream()
                .filter(col -> col.getKey().equalsIgnoreCase(columnName))
                .findFirst();
            
            if (matchingColumn.isPresent()) {
                ColumnMetaDTO enhancedColumn = matchingColumn.get();
                enhancedColumn.setDateType(rsMetaData.getColumnClassName(i));
                enhancedColumn.setDataType(rsMetaData.getColumnType(i));
                enhancedColumns.add(enhancedColumn);
                log.debug("Enhanced metadata for column: {}", columnName);
            } else {
                log.warn("No basic metadata found for column: {}", columnName);
            }
        }
        
        return enhancedColumns;
    }

    @Override
    public List<ColumnMetaDTO> getColumnMetaDataWithSql(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        OdpsSourceDTO odpsSourceDTO = (OdpsSourceDTO) iSource;
        beforeQuery(odpsSourceDTO, queryDTO, true);
        List<ColumnMetaDTO> columnList = new ArrayList<>();
        Odps odps = null;
        try {
            odps = initOdps(odpsSourceDTO);
            Instance instance = runOdpsTask(odps, queryDTO);
            ResultSet records = SQLTask.getResultSet(instance);
            TableSchema tableSchema = records.getTableSchema();
            //获取非分区字段
            tableSchema.getColumns().forEach(column -> {
                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                columnMetaDTO.setKey(column.getName());
                columnMetaDTO.setType(column.getTypeInfo().getTypeName());
                columnMetaDTO.setComment(column.getComment());
                columnList.add(columnMetaDTO);
            });
            //获取分区字段-应该不会走到这
            tableSchema.getPartitionColumns().forEach(partitionColumn -> {
                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                columnMetaDTO.setKey(partitionColumn.getName());
                columnMetaDTO.setType(partitionColumn.getTypeInfo().getTypeName());
                columnMetaDTO.setComment(partitionColumn.getComment());
                //设置为分区字段
                columnMetaDTO.setPart(true);
                columnList.add(columnMetaDTO);
            });
        } catch (Exception e) {
            throw new DtLoaderException(String.format("SQL execute error,%s", e.getMessage()), e);
        } finally {
            closeResource(odps, odpsSourceDTO);
        }
        return columnList;
    }

    @Override
    public List<String> getColumnClassInfo(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        OdpsSourceDTO odpsSourceDTO = (OdpsSourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(odpsSourceDTO, queryDTO);
        List<String> columnClassInfo = Lists.newArrayList();
        Odps odps = null;
        try {
            odps = initOdps(odpsSourceDTO);
            queryDTO.setSql("select * from " + queryDTO.getTableName());
            Instance instance = runOdpsTask(odps, queryDTO);
            ResultSet records = SQLTask.getResultSet(instance);
            TableSchema tableSchema = records.getTableSchema();
            for (Column recordColumn : tableSchema.getColumns()) {
                columnClassInfo.add(recordColumn.getTypeInfo().getTypeName());
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("SQL execute error,%s", e.getMessage()), e);
        } finally {
            closeResource(odps, odpsSourceDTO);
        }
        return columnClassInfo;
    }

    @Override
    public List<List<Object>> getPreview(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        OdpsSourceDTO odpsSourceDTO = (OdpsSourceDTO) iSource;
        List<List<Object>> dataList = new ArrayList<>();
        List<Object> columnMeta = Lists.newArrayList();
        //预览条数，默认100条
        Integer previewNum = queryDTO.getPreviewNum();
        if (StringUtils.isBlank(queryDTO.getTableName())) {
            return dataList;
        }
        Odps odps = null;
        try {
            odps = initOdps(odpsSourceDTO);
            Table t = odps.tables().get(queryDTO.getTableName());
            DefaultRecordReader recordReader;
            Map<String, String> partitionColumns = queryDTO.getPartitionColumns();
            if (MapUtils.isNotEmpty(partitionColumns)) {
                PartitionSpec partitionSpec = new PartitionSpec();
                Set<String> partSet = partitionColumns.keySet();
                for (String part : partSet) {
                    partitionSpec.set(part, partitionColumns.get(part));
                }
                recordReader = (DefaultRecordReader) t.read(partitionSpec, null, previewNum);
            } else {
                recordReader = (DefaultRecordReader) t.read(previewNum);
            }

            List<ColumnMetaDTO> metaData = getColumnMetaData(odpsSourceDTO, queryDTO);
            for (ColumnMetaDTO columnMetaDTO : metaData) {
                columnMeta.add(columnMetaDTO.getKey());
            }
            dataList.add(columnMeta);
            for (int i = 0; i < previewNum; i++) {
                List<String> result = recordReader.readRaw();
                if (CollectionUtils.isNotEmpty(result)) {
                    dataList.add(new ArrayList<>(result));
                }
            }
            return dataList;
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            closeResource(odps, odpsSourceDTO);
        }
    }

    @Override
    public List<Map<String, Object>> executeQuery(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        OdpsSourceDTO odpsSourceDTO = (OdpsSourceDTO) iSource;
        beforeQuery(odpsSourceDTO, queryDTO, true);
        List<Map<String, Object>> result = Lists.newArrayList();
        Odps odps = null;
        try {
            odps = initOdps(odpsSourceDTO);
            Instance instance = runOdpsTask(odps, queryDTO);
            ResultSet records = SQLTask.getResultSet(instance);
            TableSchema tableSchema = records.getTableSchema();
            while (records.hasNext()) {
                Record record = records.next();
                Map<String, Object> row = Maps.newLinkedHashMap();
                for (Column recordColumn : tableSchema.getColumns()) {
                    String columnName = recordColumn.getName();
                    row.put(columnName, dealColumnType(record, recordColumn));
                }
                result.add(row);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("SQL execute error : %s", e.getMessage()), e);
        } finally {
            closeResource(odps, odpsSourceDTO);
        }
        return result;
    }

    /**
     * odps类型转换java类型
     * @param record
     * @param recordColumn
     * @return
     */
    private Object dealColumnType(Record record, Column recordColumn) {
        OdpsType odpsType = recordColumn.getTypeInfo().getOdpsType();
        String columnName = recordColumn.getName();
        switch (odpsType) {
            case STRING:
                return record.getString(columnName);
            case DOUBLE:
                return record.getDouble(columnName);
            case BOOLEAN:
                return record.getBoolean(columnName);
            case DATE:
                return record.getDatetime(columnName);
            case DECIMAL:
                return record.getDecimal(columnName);
            case BIGINT:
                return record.getBigint(columnName);
            default:
                return record.get(columnName);
        }
    }

    @Override
    public Boolean executeSqlWithoutResultSet(ISourceDTO source, SqlQueryDTO queryDTO) {
        beforeQuery(source, queryDTO, true);
        OdpsSourceDTO odpsSourceDTO = (OdpsSourceDTO) source;
        Odps odps = null;
        boolean isSuccessful = false;
        try {
            odps = initOdps(odpsSourceDTO);
            Instance instance = runOdpsTask(odps, queryDTO);
            isSuccessful = instance.isSuccessful();
        } catch (Exception e) {
            throw new DtLoaderException(String.format("SQL execute error : %s", e.getMessage()), e);
        } finally {
            closeResource(odps, odpsSourceDTO);
        }

        return isSuccessful;
    }

    @Override
    public List<TableViewDTO> getTableAndViewList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        OdpsSourceDTO odpsSourceDTO = (OdpsSourceDTO) iSource;
        beforeQuery(odpsSourceDTO, queryDTO, false);
        List<TableViewDTO> resultList = new ArrayList<>();
        Odps odps = null;
        try {
            odps = initOdps(odpsSourceDTO);
            odps.tables().forEach((Table table) -> resultList.add(new TableViewDTO(table.getName(), table.isVirtualView() ? "VIEW" : "TABLE")));
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            closeResource(odps, odpsSourceDTO);
        }
        return resultList;
    }

    private void closeResource(Odps odps, OdpsSourceDTO odpsSourceDTO) {
        //归还对象
        if (BooleanUtils.isTrue(IS_OPEN_POOL.get()) && odps != null) {
            OdpsPool odpsPool = odpsManager.getConnection(odpsSourceDTO);
            odpsPool.returnResource(odps);
            IS_OPEN_POOL.remove();
        }
    }

    /**
     * 以任务的形式运行 SQL
     *
     * @param queryDTO
     * @return
     * @throws OdpsException
     */
    private Instance runOdpsTask(Odps odps, SqlQueryDTO queryDTO) throws OdpsException {
        // 查询 SQL 必须以 分号结尾
        String queryDTOSql = queryDTO.getSql();
        queryDTOSql = queryDTOSql.trim().endsWith(";") ? queryDTOSql : queryDTOSql.trim() + ";";
        Instance i = SQLTask.run(odps, queryDTOSql);
        i.waitForSuccess();
        return i;
    }

    @Override
    public String getTableMetaComment(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        OdpsSourceDTO odpsSourceDTO = (OdpsSourceDTO) iSource;
        beforeColumnQuery(odpsSourceDTO, queryDTO);
        Odps odps = null;
        try {
            odps = initOdps(odpsSourceDTO);
            Table t = odps.tables().get(queryDTO.getTableName());
            return t.getComment();
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            closeResource(odps, odpsSourceDTO);
        }
    }

    protected Integer beforeQuery(ISourceDTO iSource, SqlQueryDTO queryDTO, boolean query) {
        // 查询 SQL 不能为空
        if (query && StringUtils.isBlank(queryDTO.getSql())) {
            throw new DtLoaderException("SQL is not null");
        }

        return ConnectionClearStatus.CLOSE.getValue();
    }

    protected Integer beforeColumnQuery(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        OdpsSourceDTO odpsSourceDTO = (OdpsSourceDTO) iSource;
        Integer clearStatus = beforeQuery(odpsSourceDTO, queryDTO, false);
        if (queryDTO == null || StringUtils.isBlank(queryDTO.getTableName())) {
            throw new DtLoaderException("Query table name cannot be empty");
        }

        queryDTO.setColumns(CollectionUtils.isEmpty(queryDTO.getColumns()) ? Collections.singletonList("*") :
                queryDTO.getColumns());
        return clearStatus;
    }
}
